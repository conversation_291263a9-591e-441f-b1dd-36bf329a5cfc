from aws_cdk import Duration
from aws_cdk import aws_apigateway as apigw
from aws_cdk import aws_iam as iam
from aws_cdk import aws_lambda as _lambda
from aws_cdk import aws_logs as logs
from constructs import Construct

from .config import (
    API_STAGE_NAME,
    API_THROTTLE_BURST_LIMIT,
    API_THROTTLE_RATE_LIMIT,
    LAMBDA_ARCHITECTURE,
    LAMBDA_HANDLER,
    LAMBDA_LOG_GROUP_NAME,
    LAMBDA_LOG_RETENTION_DAYS,
    LAMBDA_MEMORY_SIZE_MB,
    LAMBDA_RUNTIME_PYTHON,
    LAMBDA_TIMEOUT_SECONDS,
)
from .helpers import (
    create_api_cors_options,
    create_cfn_output,
    create_s3_read_write_policy,
)


class LambdaApiConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        bucket_name: str,
        backend_role: iam.Role,
        allowed_origins: list[str],
    ) -> None:
        super().__init__(scope, construct_id)

        self.bucket_name = bucket_name
        self.backend_role = backend_role
        self.allowed_origins = allowed_origins

        self._create_lambda_function()
        self._create_api_gateway()
        self._create_outputs()

    def _create_lambda_function(self) -> None:
        self.lambda_execution_role = iam.Role(
            self,
            "LambdaExecutionRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            description="Execution role for Biormika Lambda API",
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaBasicExecutionRole"
                )
            ],
        )

        self.lambda_execution_role.add_to_policy(
            create_s3_read_write_policy(self.bucket_name)
        )

        log_group = logs.LogGroup(
            self,
            "ApiLogGroup",
            log_group_name=LAMBDA_LOG_GROUP_NAME,
            retention=logs.RetentionDays.ONE_WEEK,
        )

        self.api_function = _lambda.Function(
            self,
            "ApiFunction",
            runtime=getattr(_lambda.Runtime, LAMBDA_RUNTIME_PYTHON),
            architecture=getattr(_lambda.Architecture, LAMBDA_ARCHITECTURE),
            handler=LAMBDA_HANDLER,
            code=_lambda.Code.from_asset("../Backend"),
            timeout=Duration.seconds(LAMBDA_TIMEOUT_SECONDS),
            memory_size=LAMBDA_MEMORY_SIZE_MB,
            role=self.lambda_execution_role,
            environment={
                "S3_BUCKET_NAME": self.bucket_name,
                "ALLOWED_ORIGINS": ",".join(self.allowed_origins),
                "LAMBDA_ENVIRONMENT": "true",
            },
            log_group=log_group,
            description="Biormika EDF File Management API",
        )

    def _create_api_gateway(self) -> None:
        cors_options = create_api_cors_options(self.allowed_origins)

        self.api = apigw.LambdaRestApi(
            self,
            "ApiGateway",
            handler=self.api_function,
            proxy=True,
            description="Biormika EDF File Management API Gateway",
            deploy_options=apigw.StageOptions(
                stage_name=API_STAGE_NAME,
                throttling_rate_limit=API_THROTTLE_RATE_LIMIT,
                throttling_burst_limit=API_THROTTLE_BURST_LIMIT,
                metrics_enabled=True,
            ),
            default_cors_preflight_options=apigw.CorsOptions(**cors_options),
            endpoint_configuration=apigw.EndpointConfiguration(
                types=[apigw.EndpointType.REGIONAL]
            ),
        )

    def _create_outputs(self) -> None:
        create_cfn_output(
            self,
            "ApiGatewayUrl",
            value=self.api.url,
            description="API Gateway URL for the Biormika API",
            export_name="BiormikApiGatewayUrl",
        )

        create_cfn_output(
            self,
            "ApiGatewayId",
            value=self.api.rest_api_id,
            description="API Gateway ID",
            export_name="BiormikaApiGatewayId",
        )

        create_cfn_output(
            self,
            "LambdaFunctionArn",
            value=self.api_function.function_arn,
            description="Lambda function ARN",
            export_name="BiormikaLambdaFunctionArn",
        )
