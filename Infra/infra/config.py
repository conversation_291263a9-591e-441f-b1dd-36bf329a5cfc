import os
import sys
from pathlib import Path

backend_path = Path(__file__).parent.parent.parent / "Backend"
sys.path.insert(0, str(backend_path))

# isort: skip
from app.shared_constants import (
    ALLOWED_FILE_EXTENSIONS as SHARED_FILE_EXTENSIONS,
    <PERSON>WS_PROFILE as SHARED_<PERSON><PERSON>_PROFILE,
    AWS_REGION as SHARED_AWS_REGION,
    DEFAULT_ALLOWED_ORIGINS,
    ENVIRONMENT,
    FASTAPI_PORT,
    PROJECT_NAME,
    PURPOSE,
    REACT_ALT_PORT,
    S3_CORS_EXPOSED_HEADERS,
    S3_CORS_MAX_AGE_SECONDS,
    S3_MULTIPART_UPLOAD_CLEANUP_DAYS,
    STACK_DESCRIPTION,
    STACK_NAME,
    VITE_DEV_PORT,
)

LAMBDA_RUNTIME_PYTHON = "PYTHON_3_11"
LAMBDA_ARCHITECTURE = "ARM_64"
LAMBDA_TIMEOUT_SECONDS = 30
LAMBDA_MEMORY_SIZE_MB = 512
LAMBDA_LOG_RETENTION_DAYS = 7
LAMBDA_LOG_GROUP_NAME = "/aws/lambda/biormika-api"
LAMBDA_HANDLER = "lambda_handler.handler"

API_STAGE_NAME = "prod"
API_THROTTLE_RATE_LIMIT = 100
API_THROTTLE_BURST_LIMIT = 200
API_CORS_MAX_AGE_HOURS = 1

S3_ACTIONS_READ_WRITE = [
    "s3:GetObject",
    "s3:PutObject",
    "s3:DeleteObject",
    "s3:ListBucket",
    "s3:GetObjectAttributes",
    "s3:HeadObject",
]

S3_ACTIONS_PRESIGNED = [
    "s3:GetObject",
    "s3:PutObject",
    "s3:DeleteObject",
    "s3:ListBucket",
]

__all__ = [
    "AWS_PROFILE",
    "DEFAULT_REGION",
    "DEFAULT_ACCOUNT",
    "DEFAULT_ALLOWED_ORIGINS",
    "ENVIRONMENT",
    "FASTAPI_PORT",
    "PROJECT_NAME",
    "PURPOSE",
    "REACT_ALT_PORT",
    "S3_CORS_EXPOSED_HEADERS",
    "S3_CORS_MAX_AGE_SECONDS",
    "S3_MULTIPART_UPLOAD_CLEANUP_DAYS",
    "STACK_DESCRIPTION",
    "STACK_NAME",
    "VITE_DEV_PORT",
    "ALLOWED_FILE_EXTENSIONS",
    "LAMBDA_RUNTIME_PYTHON",
    "LAMBDA_ARCHITECTURE",
    "LAMBDA_TIMEOUT_SECONDS",
    "LAMBDA_MEMORY_SIZE_MB",
    "LAMBDA_LOG_RETENTION_DAYS",
    "LAMBDA_LOG_GROUP_NAME",
    "LAMBDA_HANDLER",
    "API_STAGE_NAME",
    "API_THROTTLE_RATE_LIMIT",
    "API_THROTTLE_BURST_LIMIT",
    "API_CORS_MAX_AGE_HOURS",
    "S3_ACTIONS_READ_WRITE",
    "S3_ACTIONS_PRESIGNED",
]

AWS_PROFILE = os.getenv("AWS_PROFILE", SHARED_AWS_PROFILE)
DEFAULT_REGION = os.getenv("CDK_DEFAULT_REGION", SHARED_AWS_REGION)
DEFAULT_ACCOUNT = os.getenv("CDK_DEFAULT_ACCOUNT")

ALLOWED_FILE_EXTENSIONS = [f"*{ext}" for ext in SHARED_FILE_EXTENSIONS]
