from pydantic_settings import BaseSettings
from typing import List
import os
from .shared_constants import (
    AWS_PROFILE,
    AWS_REGION,
    DEFAULT_ALLOWED_ORIGINS,
    MAX_FILE_SIZE_MB,
    ALLOWED_FILE_EXTENSIONS,
    PRESIGNED_URL_EXPIRY_SECONDS,
)


class Settings(BaseSettings):
    aws_profile: str = AWS_PROFILE
    aws_region: str = AWS_REGION
    s3_bucket_name: str
    allowed_origins: List[str] = DEFAULT_ALLOWED_ORIGINS
    max_file_size_mb: int = MAX_FILE_SIZE_MB
    allowed_file_extensions: List[str] = ALLOWED_FILE_EXTENSIONS
    presigned_url_expiry_seconds: int = PRESIGNED_URL_EXPIRY_SECONDS

    # Lambda-specific settings
    lambda_environment: bool = False

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Detect Lambda environment
        self.lambda_environment = (
            os.getenv("LAMBDA_ENVIRONMENT", "false").lower() == "true"
        )

        # Handle CORS origins from environment (Lambda)
        if self.lambda_environment:
            origins_env = os.getenv("ALLOWED_ORIGINS", "")
            if origins_env:
                self.allowed_origins = [
                    origin.strip() for origin in origins_env.split(",")
                ]

        # Only set AWS_PROFILE in local development
        if not self.lambda_environment:
            os.environ["AWS_PROFILE"] = self.aws_profile

    @property
    def is_lambda(self) -> bool:
        """Check if running in Lambda environment"""
        return self.lambda_environment


settings = Settings()
