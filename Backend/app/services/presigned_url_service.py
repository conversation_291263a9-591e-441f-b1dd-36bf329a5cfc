from typing import Dict, Any
from botocore.exceptions import ClientError

from ..config import settings
from ..logging_config import get_logger
from ..constants import (
    DEFAULT_EXPIRY_SECONDS,
    CONTENT_TYPE_OCTET_STREAM,
    METADATA_ORIGINAL_FILENAME,
    S3_CLIENT_METHOD_PUT,
    S3_CLIENT_METHOD_GET,
)

logger = get_logger(__name__)


class PresignedUrlService:
    def __init__(self, s3_client, bucket_name: str):
        self.s3_client = s3_client
        self.bucket_name = bucket_name

    def _generate_presigned_url(
        self,
        client_method: str,
        params: Dict[str, Any],
        expiry: int = DEFAULT_EXPIRY_SECONDS,
    ) -> str:
        try:
            return self.s3_client.generate_presigned_url(
                ClientMethod=client_method,
                Params=params,
                ExpiresIn=expiry,
            )
        except ClientError as e:
            logger.error(
                f"Error generating presigned URL for {client_method}: {e}"
            )
            raise

    def generate_upload_url(self, key: str, filename: str) -> str:
        params = {
            "Bucket": self.bucket_name,
            "Key": key,
            "ContentType": CONTENT_TYPE_OCTET_STREAM,
            "Metadata": {METADATA_ORIGINAL_FILENAME: filename},
        }
        return self._generate_presigned_url(
            S3_CLIENT_METHOD_PUT,
            params,
            settings.presigned_url_expiry_seconds,
        )

    def generate_download_url(
        self, key: str, expiry: int = DEFAULT_EXPIRY_SECONDS
    ) -> str:
        params = {"Bucket": self.bucket_name, "Key": key}
        return self._generate_presigned_url(S3_CLIENT_METHOD_GET, params, expiry)