import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../index';
import type { AnalyzedFile, UploadedFile, FilesState } from '@/types/files';
import type { FileInfo } from '@/types/api';
import { fileService } from '@/services/api';
import { ERROR_MESSAGES } from '@/constants';

const initialState: FilesState = {
  analyzedFiles: [],
  uploadedFiles: [],
  selectedFile: null,
  isLoading: false,
  error: null,
  operations: {
    uploading: false,
    deleting: null,
    fetchingDownload: null,
  },
};

export const fetchFilesThunk = createAsyncThunk(
  'files/fetchFiles',
  async () => {
    const response = await fileService.listFiles();
    return response.files;
  }
);

export const uploadFileThunk = createAsyncThunk(
  'files/uploadFile',
  async ({ file, onProgress }: { file: File; onProgress?: (progress: number) => void }) => {
    const presignedUrlData = await fileService.getPresignedUrl({
      filename: file.name,
      filesize: file.size,
    });
    await fileService.uploadFile(file, presignedUrlData.url, onProgress);
    const response = await fileService.listFiles();
    return response.files;
  }
);

export const deleteFileThunk = createAsyncThunk(
  'files/deleteFile',
  async (key: string) => {
    await fileService.deleteFile(key);
    return key;
  }
);

export const getDownloadUrlThunk = createAsyncThunk(
  'files/getDownloadUrl',
  async (key: string) => {
    const response = await fileService.getDownloadUrl(key);
    return { key, url: response.download_url };
  }
);

const mapFileInfoToUploadedFile = (fileInfo: FileInfo): UploadedFile => ({
  edf: fileInfo.filename,
  size: `${(fileInfo.size / 1024 / 1024).toFixed(2)} MB`,
  patient: 'Unknown',
  srHz: 0,
  recordingStartDate: fileInfo.last_modified.split('T')[0],
  recordingStartTime: fileInfo.last_modified.split('T')[1]?.split('.')[0] || '',
  recordingEndDate: fileInfo.last_modified.split('T')[0],
  recordingEndTime: fileInfo.last_modified.split('T')[1]?.split('.')[0] || '',
  recordingLength: '0:00:00',
});

export const filesSlice = createSlice({
  name: 'files',
  initialState,
  reducers: {
    setAnalyzedFiles: (state, action: PayloadAction<AnalyzedFile[]>) => {
      state.analyzedFiles = action.payload;
    },
    setUploadedFiles: (state, action: PayloadAction<UploadedFile[]>) => {
      state.uploadedFiles = action.payload;
    },
    addAnalyzedFile: (state, action: PayloadAction<AnalyzedFile>) => {
      state.analyzedFiles.push(action.payload);
    },
    addUploadedFile: (state, action: PayloadAction<UploadedFile>) => {
      state.uploadedFiles.push(action.payload);
    },
    removeUploadedFile: (state, action: PayloadAction<string>) => {
      state.uploadedFiles = state.uploadedFiles.filter(file => file.edf !== action.payload);
    },
    setSelectedFile: (state, action: PayloadAction<UploadedFile | null>) => {
      state.selectedFile = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearFiles: (state) => {
      state.analyzedFiles = [];
      state.uploadedFiles = [];
      state.selectedFile = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFilesThunk.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFilesThunk.fulfilled, (state, action) => {
        state.isLoading = false;
        state.uploadedFiles = action.payload.map(mapFileInfoToUploadedFile);
      })
      .addCase(fetchFilesThunk.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || ERROR_MESSAGES.FETCH_FAILED;
      })
      .addCase(uploadFileThunk.pending, (state) => {
        state.operations.uploading = true;
        state.error = null;
      })
      .addCase(uploadFileThunk.fulfilled, (state, action) => {
        state.operations.uploading = false;
        state.uploadedFiles = action.payload.map(mapFileInfoToUploadedFile);
      })
      .addCase(uploadFileThunk.rejected, (state, action) => {
        state.operations.uploading = false;
        state.error = action.error.message || ERROR_MESSAGES.UPLOAD_FAILED;
      })
      .addCase(deleteFileThunk.pending, (state, action) => {
        state.operations.deleting = action.meta.arg;
        state.error = null;
      })
      .addCase(deleteFileThunk.fulfilled, (state, action) => {
        state.operations.deleting = null;
        state.uploadedFiles = state.uploadedFiles.filter(file => file.edf !== action.payload);
      })
      .addCase(deleteFileThunk.rejected, (state, action) => {
        state.operations.deleting = null;
        state.error = action.error.message || ERROR_MESSAGES.DELETE_FAILED;
      })
      .addCase(getDownloadUrlThunk.pending, (state, action) => {
        state.operations.fetchingDownload = action.meta.arg;
      })
      .addCase(getDownloadUrlThunk.fulfilled, (state) => {
        state.operations.fetchingDownload = null;
      })
      .addCase(getDownloadUrlThunk.rejected, (state, action) => {
        state.operations.fetchingDownload = null;
        state.error = action.error.message || 'Failed to generate download link';
      });
  },
});

export const {
  setAnalyzedFiles,
  setUploadedFiles,
  addAnalyzedFile,
  addUploadedFile,
  removeUploadedFile,
  setSelectedFile,
  clearError,
  clearFiles,
} = filesSlice.actions;

export const selectFiles = (state: RootState) => state.files;

export default filesSlice.reducer;