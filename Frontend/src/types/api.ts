export interface PresignedUrlRequest {
  filename: string;
  filesize: number;
}

export interface PresignedUrlResponse {
  url: string;
  key: string;
  expires_in: number;
}

export interface FileInfo {
  key: string;
  filename: string;
  size: number;
  last_modified: string;
  etag?: string;
}

export interface FileListResponse {
  files: FileInfo[];
  total_count: number;
  total_size: number;
}

export interface FileDeleteResponse {
  success: boolean;
  message: string;
  deleted_key: string;
}

export interface ApiError {
  detail: string;
  status?: number;
}

export interface HealthCheckResponse {
  status: string;
  timestamp: string;
}