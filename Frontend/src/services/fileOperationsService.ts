import { fileService } from "./api";
import { ERROR_MESSAGES } from "@/constants";
import type { FileInfo } from "@/types/api";

export type OperationResult<T = void> = {
  success: boolean;
  data?: T;
  error?: string;
};

export class FileOperationsService {
  static async uploadFile(
    file: File,
    onProgress?: (percent: number) => void
  ): Promise<OperationResult> {
    try {
      const presignedUrlData = await fileService.getPresignedUrl({
        filename: file.name,
        filesize: file.size,
      });
      await fileService.uploadFile(file, presignedUrlData.url, onProgress);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.UPLOAD_FAILED,
      };
    }
  }

  static async deleteFile(key: string): Promise<OperationResult> {
    try {
      await fileService.deleteFile(key);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.DELETE_FAILED,
      };
    }
  }

  static async fetchFiles(): Promise<OperationResult<FileInfo[]>> {
    try {
      const response = await fileService.listFiles();
      return { success: true, data: response.files };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.FETCH_FAILED,
      };
    }
  }

  static async getDownloadUrl(key: string): Promise<OperationResult<string>> {
    try {
      const response = await fileService.getDownloadUrl(key);
      return { success: true, data: response.download_url };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.DOWNLOAD_FAILED,
      };
    }
  }

  static async performFileAction<T>(
    action: () => Promise<T>,
    errorMessage?: string
  ): Promise<OperationResult<T>> {
    try {
      const result = await action();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : errorMessage || "Operation failed",
      };
    }
  }
}

export const fileOperations = FileOperationsService;